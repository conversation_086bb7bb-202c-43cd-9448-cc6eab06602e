# Zoho Chat Widget Integration

This document describes the integration of Zoho SalesIQ Chat Widget into the React application.

## Overview

The Zoho Chat Widget has been integrated to provide customer support functionality. The widget dynamically loads based on the user's role and passes user information to Zoho SalesIQ.

## Implementation

### Files Added/Modified

1. **`src/components/ZohoChatWidget.js`** - Main widget component
2. **`src/App.js`** - Integration point in the main application
3. **`src/components/ZohoChatWidget.test.js`** - Unit tests for the widget

### Component Features

- **Role-based Widget Loading**: Different widget configurations for Admin and User roles
- **User Information Passing**: Automatically passes logged-in user's name, role, and department
- **Cookie Management**: Clears existing Zoho cookies before loading new widget
- **Script Management**: Removes old widget scripts before injecting new ones

### Widget Configuration

The component supports multiple user roles:

- **Admin**: Uses dedicated Admin widget code
- **User**: Uses User widget code (default fallback)
- **Dealer**: Uses User widget code
- **Supplier**: Uses User widget code  
- **External User**: Uses User widget code

### User Information Mapping

The widget receives the following user information:

- **Name**: `config.information.empname` (from Redux state)
- **Role**: Mapped from `config.role`:
  - `clientadmin` → `Admin`
  - `clientuser` → `User`
  - `clientdealer` → `Dealer`
  - `clientsupplier` → `Supplier`
  - `clientextuser` → `External User`
- **Department**: `admin_data.information.companyname` (from Redux state)

## Configuration

### Widget Codes

Update the widget codes in `src/components/ZohoChatWidget.js`:

```javascript
const WIDGETS = {
  Admin: {
    widgetcode: 'YOUR_ADMIN_WIDGET_CODE',
    scriptUrl: 'https://salesiq.zohopublic.in/widget?wc=YOUR_ADMIN_WIDGET_CODE',
  },
  User: {
    widgetcode: 'YOUR_USER_WIDGET_CODE', 
    scriptUrl: 'https://salesiq.zohopublic.in/widget?wc=YOUR_USER_WIDGET_CODE',
  },
  // ... other roles
};
```

### Integration Point

The widget is rendered in `src/App.js` when:
- User is authenticated (`localStorage.getItem('token')` exists)
- User configuration is loaded (`config` and `admin_data` are available)

```javascript
{Object.keys(config).length !== 0 && Object.keys(admin_data).length !== 0 && (
    <ZohoChatWidget 
        name={config?.information?.empname || 'User'}
        role={/* role mapping logic */}
        department={admin_data?.information?.companyname || 'Default Department'}
    />
)}
```

## Usage

The widget automatically loads when a user logs in and has the necessary user information available. No additional setup is required from the user's perspective.

## Testing

Run the unit tests:

```bash
npm test -- ZohoChatWidget.test.js
```

## Troubleshooting

### Widget Not Loading

1. Check browser console for JavaScript errors
2. Verify widget codes are correct in the configuration
3. Ensure user is properly authenticated and user data is available

### Multiple Widgets Loading

The component automatically removes previous widget scripts before loading new ones. If you see multiple widgets, check for other Zoho integrations in the codebase.

### User Information Not Passing

Verify that:
- Redux state contains `config.information.empname`
- Redux state contains `admin_data.information.companyname`
- User role mapping is working correctly

## Security Considerations

- Widget codes should be treated as sensitive configuration
- Consider using environment variables for widget codes in production
- The widget only loads for authenticated users

## Future Enhancements

- Add support for additional user roles
- Implement widget customization based on user preferences
- Add analytics tracking for widget usage
