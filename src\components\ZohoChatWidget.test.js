import React from 'react';
import { render } from '@testing-library/react';
import ZohoChatWidget from './ZohoChatWidget';

global.window = Object.create(window);

describe('ZohoChatWidget', () => {
  beforeEach(() => {
    document.head.innerHTML = '';
    document.body.innerHTML = '';
    delete window.$zoho;
  });

  test('renders Admin widget correctly', () => {
    render(
      <ZohoChatWidget
        name="Mahesh"
        role="Admin"
        department="Admin Department"
      />
    );

    const script = document.getElementById('zsiqscript');
    expect(script).toBeTruthy();
    expect(script.src).toContain('siqb95d6ef899c13858d7f5f37fa6171acbe5201291a5ec9676108f4bb3fd896076');
    expect(window.$zoho).toBeDefined();
    expect(window.$zoho.salesiq).toBeDefined();
    expect(window.$zoho.salesiq.widgetcode).toBe('siqb95d6ef899c13858d7f5f37fa6171acbe5201291a5ec9676108f4bb3fd896076');
  });
});
