import React from 'react';
import ZohoChatWidget from './ZohoChatWidget';

/**
 * Example usage of ZohoChatWidget component
 * This file demonstrates how to use the widget in different scenarios
 */

// Example 1: Admin User
const AdminExample = () => {
  return (
    <div>
      <h1>Welcome to the Admin Dashboard</h1>
      <ZohoChatWidget 
        name="<PERSON><PERSON><PERSON>" 
        role="Admin" 
        department="Administration"
      />
    </div>
  );
};

// Example 2: Regular User
const UserExample = () => {
  return (
    <div>
      <h1>Welcome to the User Portal</h1>
      <ZohoChatWidget 
        name="<PERSON>" 
        role="User" 
        department="Operations"
      />
    </div>
  );
};

// Example 3: Dealer User
const DealerExample = () => {
  return (
    <div>
      <h1>Dealer Dashboard</h1>
      <ZohoChatWidget 
        name="<PERSON>" 
        role="Dealer" 
        department="Sales"
      />
    </div>
  );
};

// Example 4: Supplier User
const SupplierExample = () => {
  return (
    <div>
      <h1>Supplier Portal</h1>
      <ZohoChatWidget 
        name="ABC Supplies Inc" 
        role="Supplier" 
        department="Supply Chain"
      />
    </div>
  );
};

// Example 5: External User
const ExternalUserExample = () => {
  return (
    <div>
      <h1>External User Access</h1>
      <ZohoChatWidget 
        name="External Consultant" 
        role="External User" 
        department="Consulting"
      />
    </div>
  );
};

// Example 6: Dynamic role based on props
const DynamicRoleExample = ({ userInfo }) => {
  const getRoleDisplayName = (role) => {
    const roleMap = {
      'clientadmin': 'Admin',
      'clientuser': 'User',
      'clientdealer': 'Dealer',
      'clientsupplier': 'Supplier',
      'clientextuser': 'External User'
    };
    return roleMap[role] || 'User';
  };

  return (
    <div>
      <h1>Dynamic Role Example</h1>
      <p>Current user: {userInfo.name}</p>
      <p>Role: {getRoleDisplayName(userInfo.role)}</p>
      <ZohoChatWidget 
        name={userInfo.name}
        role={getRoleDisplayName(userInfo.role)}
        department={userInfo.department}
      />
    </div>
  );
};

// Example usage with sample data
const sampleUserInfo = {
  name: "Sample User",
  role: "clientuser",
  department: "Sample Department"
};

// Main example component that shows all scenarios
const ZohoChatWidgetExamples = () => {
  return (
    <div style={{ padding: '20px' }}>
      <h1>Zoho Chat Widget Examples</h1>
      
      <div style={{ marginBottom: '40px' }}>
        <h2>1. Admin Example</h2>
        <AdminExample />
      </div>

      <div style={{ marginBottom: '40px' }}>
        <h2>2. User Example</h2>
        <UserExample />
      </div>

      <div style={{ marginBottom: '40px' }}>
        <h2>3. Dealer Example</h2>
        <DealerExample />
      </div>

      <div style={{ marginBottom: '40px' }}>
        <h2>4. Supplier Example</h2>
        <SupplierExample />
      </div>

      <div style={{ marginBottom: '40px' }}>
        <h2>5. External User Example</h2>
        <ExternalUserExample />
      </div>

      <div style={{ marginBottom: '40px' }}>
        <h2>6. Dynamic Role Example</h2>
        <DynamicRoleExample userInfo={sampleUserInfo} />
      </div>
    </div>
  );
};

export default ZohoChatWidgetExamples;
export {
  AdminExample,
  UserExample,
  DealerExample,
  SupplierExample,
  ExternalUserExample,
  DynamicRoleExample
};
