import React, { useEffect, useState } from 'react';
import { Accordion, AccordionTab } from 'primereact/accordion';
import { Button } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
import { Checkbox } from 'primereact/checkbox';
import { Dropdown } from 'primereact/dropdown';
import { InputNumber } from 'primereact/inputnumber';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { RadioButton } from 'primereact/radiobutton';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';

import APIServices from '../service/APIService';
import { API } from '../constants/api_url';
import useForceUpdate from "use-force-update";
import moment from "moment";
import Swal from 'sweetalert2';

const SectionBox = ({ data, onUpdateResponse, userlist }) => {
  const [open, setOpen] = useState(false);
  const [inputValue, setInputValue] = useState(() => {
    if (data.type === 'checkbox-group') return [];
    if (data.type === 'tableadd') return [];
    return '';
  });
  const [fieldName, setFieldName] = useState(null)
  const [consolidateText, setConsolidateText] = useState('');
  const forceUpdate = useForceUpdate();

  useEffect(() => {
    const consolidate = Object.entries(data?.response || {}).flatMap(([a, b]) => b)?.find(x => x.user === 'Consolidate') || {}
    if (consolidate.answer != null) {
      if (['select', 'radio-group', 'checkpoint'].includes(data.type)) {
        consolidate.res = data.values?.find(x => x.label === consolidate.answer)?.value
      } else if (data.type === 'checkbox-group') {
        consolidate.res = data.values?.filter(x => consolidate?.answer?.includes(x.label)).map(x => x.value)
      } else if (data.type === 'tableadd') {
        consolidate.res = consolidate.answer || []
      } else {
        consolidate.res = consolidate.answer
      }
      // Only set inputValue if there's actual consolidate data
      setInputValue(consolidate.res)
    } else {
      // Only set default value if no consolidate data exists
      const defaultValue = data.type === 'checkbox-group' ? [] : data.type === 'tableadd' ? [] : '';
      setInputValue(defaultValue);
    }
    setConsolidateText(consolidate.comment || '')
    console.log('UseEffect consolidate:', consolidate)
  }, [data.name, data.type]) // Only depend on data.name and data.type, not entire data object

  // Early return for paragraph type - AFTER all hooks
  if (data.type === 'paragraph') return null;

  // Debug the data structure
  console.log('=== SECTIONBOX DATA DEBUG ===');
  console.log('data:', data);
  console.log('data.type:', data.type);
  console.log('data.headers:', data.value);
  console.log('data.newrow:', data.newrow);
  console.log('inputValue:', inputValue);
  const getUser = (id) => {
    let user_name = 'Not Found'
    let index = userlist.findIndex(i => i.id === Number(id))
    if (index !== -1) {
      user_name = userlist[index].information.empname
    }
    return user_name
  }

  // TableAdd helper functions - exact copy from QualitativeData.js
  const addRow = (newrowData, field, index) => {
    console.log('=== ADD ROW DEBUG ===');
    console.log('Adding row to table index:', index);
    console.log('Field name:', field.name);
    console.log('Current inputValue:', inputValue);
    console.log('New row data:', newrowData);
    console.log('Field headers:', field.value);

    const current = Array.isArray(inputValue) ? inputValue : [];

    // If newrow data exists, use it; otherwise create default structure
    if (newrowData && newrowData[0]) {
      const updated = [...current, newrowData[0]];
      setInputValue(updated);
    } else {
      // Create default row structure based on headers in field.value
      const defaultRow = {};
      if (field.value && field.value.length > 0) {
        field.value.forEach(header => {
          defaultRow[header] = {
            type: 1, // default to text type
            data: { value: '' }
          };
        });
        const updated = [...current, defaultRow];
        console.log('Created default row:', defaultRow);
        setInputValue(updated);
      }
    }
    forceUpdate();
  }

  const deleteRow = (rowindex, field, tableIndex) => {
    console.log('=== DELETE ROW DEBUG ===');
    console.log('Deleting row index:', rowindex);
    console.log('From table index:', tableIndex);
    console.log('Field name:', field.name);
    console.log('Current data:', inputValue);

    const current = Array.isArray(inputValue) ? inputValue : [];
    const updated = current.filter((_, index) => index !== rowindex);
    setInputValue(updated);
    forceUpdate();
  }

  const getObjectAtIndex = (data, index) => {
    const keys = Object.keys(data);
    const key = keys[index];
    return data[key];
  };

  const onCellEditComplete = (e, field) => {
    try {
      let { rowData, newValue, cellIndex, field: columnField, rowIndex, headerName } = e;
      console.log('=== CELL EDIT COMPLETE ===');
      console.log('Event:', e);
      console.log('Field:', field);
      console.log('Row Data:', rowData);
      console.log('Column Field:', columnField);
      console.log('Row Index:', rowIndex);
      console.log('Cell Index:', cellIndex);
      console.log('New value:', newValue);
      console.log('Row data column field value:', rowData[columnField]);
      console.log('Current inputValue structure:', inputValue);
      console.log('Field headers:', field.value);
      console.log('Current row data:', Array.isArray(inputValue) && inputValue[rowIndex] ? inputValue[rowIndex] : 'No data');

      // Get the actual value - handle different scenarios
      let actualValue;

      // Use the same approach as reference implementation - get value from rowData[field]
      if (rowData[columnField] !== undefined && rowData[columnField] !== null) {
        // This is where DataTable puts the new value from the editor
        actualValue = rowData[columnField];
        console.log('Using rowData[columnField] (DataTable value):', actualValue);
      } else if (newValue !== undefined && newValue !== null) {
        // Fallback to newValue
        actualValue = newValue;
        console.log('Using newValue:', actualValue);
      } else {
        console.log('No value found, skipping update');
        return;
      }

      console.log('Final actualValue to save:', actualValue);

      // Follow the same pattern as reference code
      let loc = JSON.parse(JSON.stringify(inputValue));
      setTimeout(() => {
        try {
          console.log('Before update - loc structure:', Array.isArray(loc) && loc[rowIndex] ? loc[rowIndex] : 'No field data');
          console.log('Updating with headerName:', headerName, 'cellIndex:', cellIndex, 'actualValue:', actualValue);

          // Ensure the field data structure exists
          if (!Array.isArray(loc)) {
            loc = [];
          }
          if (!loc[rowIndex]) {
            console.warn('Row data not found at index:', rowIndex);
            return;
          }

          // Use headerName if available, otherwise fall back to cellIndex approach
          if (headerName && loc[rowIndex][headerName]) {
            // Update using header name directly
            console.log('Before update - cell data:', loc[rowIndex][headerName]);
            if (!loc[rowIndex][headerName].data) {
              loc[rowIndex][headerName].data = {};
            }
            loc[rowIndex][headerName].data['value'] = actualValue;
            console.log('Updated via header name:', headerName, 'with value:', actualValue);
            console.log('After update - cell data:', loc[rowIndex][headerName]);
          } else {
            // Fallback to cellIndex approach
            let item = getObjectAtIndex(loc[rowIndex], cellIndex);
            console.log('Item found at cellIndex:', item);

            // Ensure the data object exists
            if (!item.data) {
              item.data = {};
            }

            item.data['value'] = actualValue;
            console.log('Updated item.data.value to:', actualValue);

            // Also try to update using header name as backup
            const headerNameFromIndex = field.value[cellIndex];
            console.log('Header name for cellIndex:', headerNameFromIndex);

            if (loc[rowIndex][headerNameFromIndex]) {
              if (!loc[rowIndex][headerNameFromIndex].data) {
                loc[rowIndex][headerNameFromIndex].data = {};
              }
              loc[rowIndex][headerNameFromIndex].data['value'] = actualValue;
              console.log('Updated via header name as well');
            }
          }

          // Clean up temporary field (like reference implementation)
          delete loc[rowIndex][columnField];

          console.log('After update - loc structure:', loc[rowIndex]);

          setInputValue(loc);
          console.log('Updated inputValue:', loc);
          forceUpdate();
        } catch (updateError) {
          console.error('Error updating cell data:', updateError);
          console.error('Error details:', updateError.stack);
        }
      }, 100);

    } catch (error) {
      console.error('Error in onCellEditComplete:', error);
    }
  };

  const renderTableData = (rowData) => {
    try {
      if (!rowData || !rowData.data) {
        return <div>No data</div>;
      }

      const isError = rowData.data.error === 1;
      const cellStyle = {
        color: isError ? 'red' : 'inherit',
        cursor: rowData.type === 5 ? 'default' : 'pointer',
        padding: '8px',
        minHeight: '30px',
        display: 'flex',
        alignItems: 'center',
        width: '100%'
      };

      const currentValue = rowData.data.value;

      if (rowData.type === 5) {
        return (
          <div style={cellStyle}>
            {rowData.data.label || 'Label'}
          </div>
        );
      }

      // Handle different field types for display (matching QualitativeResponse.js format)
      const hasError = rowData.data && rowData.data.error === 1;
      const errorStyle = hasError ? { border: '2px solid red' } : {};

      switch (rowData.type) {
        case 1: // text
        case 2: // textarea
        case 3: // number
          return (
            <div style={{
              ...cellStyle,
              ...errorStyle,
              color: (!currentValue || currentValue === '') ? '#999' : 'inherit',
              fontStyle: (!currentValue || currentValue === '') ? 'italic' : 'normal'
            }}>
              {currentValue || 'Click here'}
            </div>
          );
        case 4: // dropdown
          let selectedOption = null;
          try {
            if (rowData.data.values && currentValue !== null && currentValue !== undefined && currentValue !== '') {
              selectedOption = rowData.data.values.find(opt => opt.value === currentValue);
              return (
                <div style={{...cellStyle, ...errorStyle}}>
                  {selectedOption ? selectedOption.label : currentValue}
                </div>
              );
            }
          } catch (error) {
            console.error('Error finding selected option:', error);
          }
          return (
            <div style={{...cellStyle, ...errorStyle, color: '#999', fontStyle: 'italic'}}>
              Click here
            </div>
          );
        case 5: // label
          return (
            <div style={cellStyle}>
              {rowData.data.label || 'Click here'}
            </div>
          );
        case 6: // date
          let dateDisplay = '';
          try {
            if (currentValue) {
              dateDisplay = moment(currentValue).format('DD-MM-YYYY');
            }
          } catch (error) {
            console.error('Error formatting date:', error);
            dateDisplay = 'Invalid date';
          }
          return (
            <div style={{
              ...cellStyle,
              ...errorStyle,
              color: !dateDisplay ? '#999' : 'inherit',
              fontStyle: !dateDisplay ? 'italic' : 'normal'
            }}>
              {dateDisplay || 'Click here'}
            </div>
          );
        default:
          return (
            <div style={{
              ...cellStyle,
              ...errorStyle,
              color: (!currentValue || currentValue === '') ? '#999' : 'inherit',
              fontStyle: (!currentValue || currentValue === '') ? 'italic' : 'normal'
            }}>
              {currentValue || 'Click here'}
            </div>
          );
      }
    } catch (error) {
      console.error('Error in renderTableData:', error);
      return (
        <div>Error rendering data</div>
      );
    }
  }

  const renderEditor = (options, cellData) => {
    try {
      // Get item by cellIndex from field or use headerName if available
      let item;
      if (options.headerName && options.rowData[options.headerName]) {
        item = options.rowData[options.headerName];
      } else if (cellData) {
        item = cellData;
      } else {
        item = getObjectAtIndex(options.rowData, parseInt(options.field.split('_')[1]));
      }

      console.log('=== RENDER EDITOR DEBUG ===');
      console.log('Editor for field:', options.field, 'headerName:', options.headerName);
      console.log('Item:', item);

      if (!item) {
        console.warn('No item found for editor');
        return (
          <InputText
            type="text"
            defaultValue=""
            onChange={(e) => options.editorCallback(e.target.value)}
            autoFocus
            style={{ width: '100%' }}
          />
        );
      }

      // Get the current value - prioritize the passed value from options
      let currentValue = '';
      if (options.value !== undefined && options.value !== null) {
        currentValue = options.value;
      } else if (item.data && item.data.value !== undefined) {
        currentValue = item.data.value;
      }

      console.log('Current value for editor:', currentValue);
      console.log('Item type:', item.type);
      console.log('Item data:', item.data);

      switch (item.type) {
        case 1:
          return (<InputText
            type="text"
            defaultValue={currentValue}
            onChange={(e) => options.editorCallback(e.target.value)}
            autoFocus
            style={{ width: '100%' }}
          />);
        case 2:
          return (<InputTextarea
            defaultValue={currentValue}
            onChange={(e) => options.editorCallback(e.target.value)}
            autoFocus
            style={{ width: '100%' }}
          />);
        case 3:
          return (<InputText
            type="number"
            defaultValue={currentValue}
            onChange={(e) => options.editorCallback(e.target.value)}
            autoFocus
            style={{ width: '100%' }}
          />);
        case 4:
          return (<Dropdown
            optionLabel="label"
            optionValue="value"
            value={currentValue}
            options={item.data?.values || []}
            onChange={(e) => {
              console.log('Dropdown onChange:', e.value);

              // Update the local state immediately for better UX
              const headerName = options.headerName;
              const rowIndex = options.rowIndex;

              if (headerName && rowIndex !== undefined) {
                let loc = JSON.parse(JSON.stringify(inputValue));
                if (Array.isArray(loc) && loc[rowIndex] && loc[rowIndex][headerName]) {
                  if (!loc[rowIndex][headerName].data) {
                    loc[rowIndex][headerName].data = {};
                  }
                  loc[rowIndex][headerName].data.value = e.value;
                  console.log('Updated cell data:', loc[rowIndex][headerName]);

                  setInputValue(loc);
                  forceUpdate();
                  console.log('InputValue updated and component re-rendered');
                }

                // Also update rowData for DataTable
                if (options.field) {
                  options.rowData[options.field] = e.value;
                }

                options.editorCallback(e.value);
              }
            }}
            placeholder="Select option"
            autoFocus
            style={{ width: '100%' }}
            panelStyle={{ zIndex: 9999 }}
          />);
        case 6:
          return (<Calendar
            dateFormat="dd/mm/yy"
            value={currentValue}
            onChange={(e) => {
              console.log('Calendar onChange:', e.value);
              options.editorCallback(e.value);
            }}
            style={{ width: '100%' }}
            panelStyle={{ zIndex: 9999 }}
          />);
        case 5:
          return null;
        default:
          return (<InputText
            type="text"
            defaultValue={currentValue}
            onChange={(e) => options.editorCallback(e.target.value)}
            autoFocus
            style={{ width: '100%' }}
          />);
      }
    } catch (error) {
      console.error('Error in renderEditor:', error);
      return (
        <InputText
          type="text"
          value={options.value || ''}
          onChange={(e) => {
            try {
              options.editorCallback(e.target.value);
            } catch (err) {
              console.error('Error in fallback editor callback:', err);
            }
          }}
          autoFocus
          style={{ width: '100%' }}
        />
      );
    }
  }

  const actionTemplate = (rowData, rowindex, field, tableIndex) => {
    console.log('=== ACTION TEMPLATE DEBUG ===');
    console.log('Table index:', tableIndex);
    console.log('Row index:', rowindex);
    console.log('Field name:', field?.name);

    return (
      <>
        <Button
          icon="pi pi-trash"
          className="mr-2 actionbtn"
          style={{
            width: '20px',
            height: '20px',
            background: 'transparent',
            color: 'palevioletred'
          }}
          onClick={() => {
            console.log('Deleting row', rowindex, 'from table', tableIndex);
            deleteRow(rowindex, field, tableIndex);
          }}
        />
      </>
    )
  };



  const frameworkMap = {
    2: "GRI",
    3: "ISSB",
    4: "MCfS",
    5: "SASB",
    6: "TCFD",
    7: "BRSR",
    8: "SGX",
    9: "Boursa Kuwait",
    10: "Bursa Malaysia",
    11: "HKEX",
    12: "NASDAQ",
    13: "CDP",
    14: "EcoVadis",
    15: "CDP",
    16: "EcoVadis",
    17: "MSCI",
    18: "S&P Dow Jones",
    19: "Sustainalitics",
    20: "ISS",
    21: "Carbon Footprint",
    22: "GEF capital",
    24: "BRSR Core",
    25: "CSRD",
    26: "DJSI",
    27: "ESRS",
    28: "IFRS S1",
    29: "IFRS S2",
  };
  const defaultUser = 'Consolidate';
  // 👇 Mapping function
  const getDisplayType = (type) => {
    const typeMap = {
      text: 'descriptive',
      textarea: 'descriptive',
      checkbox: 'multi-select',
      'checkbox-group': 'multi-select',
      radio: 'single-select',
      'radio-group': 'single-select',
      'select-single': 'single-select',
      'select-multi': 'multi-select',
      file: 'file-upload',
      number: 'numeric',
      date: 'date',
      'datetime-local': 'date',
      tableadd: 'table-data',
    };
    return typeMap[type] || type;
  };

  const displayType = getDisplayType(data.type);

  const getTagColor = (type) => {
    const display = getDisplayType(type);
    switch (display) {
      case 'single-select':
      case 'checkpoint':
        return ['#dbf7d7', '#61be53'];
      case 'file-upload':
        return ['#f4d8e9', '#8a276e'];
      case 'multi-select':
        return ['#cce5ff', '#004085'];
      case 'numeric':
        return ['#fbe8a6', '#a67c00'];
      case 'table-data':
        return ['#e6f3ff', '#0066cc'];
      default:
        return ['#f4e9c4', '#e99e34'];
    }
  };

  const handleSave = (question, status = "In Progress") => {
    console.log('=== HANDLE SAVE DEBUG ===');
    console.log('Question:', question);
    console.log('Status:', status);
    console.log('Data type:', data.type);
    console.log('Current inputValue:', inputValue);
    console.log('Data name:', data.name);

    let finalAnswer = inputValue;
    let hasValidData = true;

    if (['select', 'radio-group', 'checkpoint'].includes(data.type)) {
      const selected = data.values?.find(opt => opt.label === inputValue || opt.value === inputValue);
      finalAnswer = selected?.label || '';
    }

    if (data.type === 'checkbox-group') {
      finalAnswer = inputValue.filter(val =>
        data.values?.some(opt => opt.label === val || opt.value === val)
      );
    }

    if (data.type === 'tableadd') {
      // For tableadd, use the inputValue directly as it's an array
      console.log('Processing tableadd...');
      console.log('inputValue structure:', inputValue);
      console.log('Data name:', data.name);

      finalAnswer = Array.isArray(inputValue) ? inputValue : [];

      // Validation for tableadd fields
      // Check if tableadd is required and has data
      if (data.required && (!Array.isArray(inputValue) || inputValue.length === 0)) {
        hasValidData = false;
        console.warn('Required tableadd field is empty:', data.name);
      }

      // Validate nested fields in tableadd data
      if (Array.isArray(inputValue) && inputValue.length > 0) {
        inputValue.forEach((row, rowIndex) => {
          Object.values(row).forEach((field) => {
            if (field.type === 1 && field.data && field.data.required) { // text
              if (!field.data.value || field.data.value.trim().length === 0) {
                field.data.error = 1;
                hasValidData = false;
                console.warn(`Required text field empty in row ${rowIndex}`);
              } else {
                delete field.data.error;
              }
            } else if (field.type === 2 && field.data && field.data.required) { // textarea
              if (!field.data.value || field.data.value.trim().length === 0) {
                field.data.error = 1;
                hasValidData = false;
                console.warn(`Required textarea field empty in row ${rowIndex}`);
              } else {
                delete field.data.error;
              }
            } else if (field.type === 3 && field.data && field.data.required) { // number
              if (field.data.value === undefined || isNaN(field.data.value) || field.data.value.toString().trim().length === 0) {
                field.data.error = 1;
                hasValidData = false;
                console.warn(`Required number field empty in row ${rowIndex}`);
              } else {
                delete field.data.error;
              }
            } else if (field.type === 4 && field.data && field.data.required) { // dropdown
              if (field.data.value === undefined || field.data.value === null) {
                field.data.error = 1;
                hasValidData = false;
                console.warn(`Required dropdown field empty in row ${rowIndex}`);
              } else {
                delete field.data.error;
              }
            } else if (field.type === 6 && field.data && field.data.required) { // date
              if (field.data.value === undefined || field.data.value === null) {
                field.data.error = 1;
                hasValidData = false;
                console.warn(`Required date field empty in row ${rowIndex}`);
              } else {
                delete field.data.error;
              }
            }
          });
        });
      }
    } // Close the main tableadd if statement

    // Show validation error if validation failed
    if (!hasValidData) {
      console.error("Validation failed - please fill all required fields");
      // Force update to show error styling
      forceUpdate();
      // Show SweetAlert for validation error
      Swal.fire({
        position: "center",
        icon: "warning",
        title: "Please fill all mandatory fields",
        text: "Highlighted cells are required to be filled",
        showConfirmButton: true,
        confirmButtonText: "OK"
      });
      return false;
    }

    console.log('Final answer for', question ? question.name : data.name, ':', finalAnswer);

    onUpdateResponse({
      ...(finalAnswer != null && finalAnswer !== ''  ? { [data.name]: finalAnswer } : {}),
      [`${data.name}_comments`]: consolidateText
    });

    // Don't reset inputValue for tableadd - keep the data
    console.log('Before state reset - data.type:', data.type);
    if (data.type !== 'tableadd') {
      console.log('Resetting state for non-tableadd');
      setInputValue(data.type === 'checkbox-group' ? [] : '');
    } else {
      console.log('Keeping tableadd data - NOT resetting inputValue');
    }
    setConsolidateText('');

    return true;
  };

  const handleCancel = () => {
    setInputValue(data.type === 'checkbox-group' ? [] : data.type === 'tableadd' ? [] : '');
    setConsolidateText('');
  };

  const [bg, color] = getTagColor(data.type);

  const inputTypes = [
    'text', 'textarea', 'number', 'select',
    'radio-group', 'checkbox-group', 'file',
    'date', 'checkpoint', 'tableadd'
  ];

  const responsesToShow = (data.response && data.response.length > 0)
    ? data.response.filter(response => {
        // For tableadd, only show responses that have tableadd data for this specific question
        if (data.type === 'tableadd') {
          return response.answer && Array.isArray(response.answer) && response.answer.length > 0;
        }
        // For other types, show all responses for this question
        return true;
      })
    : [];

  const renderAnswer = (answer) => {
    if (data.type === 'file') {
      return <a href={answer?.includes('api.eisqr.com') ? answer : API.Docs + answer} >{answer} </a>
    }

    if (data.type === 'tableadd') {
      if (!Array.isArray(answer) || answer.length === 0) {
        return 'No data available';
      }

      return (
        <div className="border rounded p-2 bg-white mt-2">
          <DataTable
            value={answer}
            showGridlines
            className="p-datatable-sm"
            style={{ width: '100%' }}
            scrollable
            scrollHeight="200px"
          >
            {data.headers && data.headers.map((header, headerIndex) => (
              <Column
                key={headerIndex}
                field={header}
                header={header}
                body={(rowData) => {
                  const cellData = rowData[header];
                  if (!cellData || !cellData.data) return '-';

                  const value = cellData.data.value;

                  switch (cellData.type) {
                    case 1:
                    case 2:
                    case 3:
                      return value || '-';
                    case 4:
                      if (cellData.data.values && value !== null && value !== undefined) {
                        const selectedOption = cellData.data.values.find(opt => opt.value === value);
                        return selectedOption ? selectedOption.label : value;
                      }
                      return value || '-';
                    case 5:
                      return cellData.data.label || '-';
                    case 6:
                      return value ? moment(value).format('DD-MM-YYYY') : '-';
                    default:
                      return value || '-';
                  }
                }}
              />
            ))}
          </DataTable>
        </div>
      );
    }

    if (Array.isArray(answer)) {
      return answer
        .map(val => data.values?.find(opt => opt.value === val || opt.label === val)?.label || val)
        .filter(Boolean)
        .join(', ');
    }
    return data.values?.find(opt => opt.value === answer || opt.label === answer)?.label || answer;
  };

  return (
    <>
      <style>
        {`
          .p-datatable .p-datatable-tbody > tr > td .p-inputtext,
          .p-datatable .p-datatable-tbody > tr > td .p-inputtextarea,
          .p-datatable .p-datatable-tbody > tr > td .p-dropdown,
          .p-datatable .p-datatable-tbody > tr > td .p-calendar {
            width: 100% !important;
            min-width: 120px !important;
            z-index: 1000 !important;
            position: relative !important;
            background: white !important;
            border: 1px solid #ced4da !important;
          }

          .p-datatable .p-datatable-tbody > tr > td.p-cell-editing {
            padding: 2px !important;
            background: #f8f9fa !important;
          }

          .p-datatable .p-datatable-tbody > tr > td.p-cell-editing > * {
            width: 100% !important;
          }

          .p-dropdown-panel,
          .p-calendar-panel {
            z-index: 9999 !important;
          }

          .p-datatable .p-datatable-tbody > tr > td .p-dropdown .p-dropdown-trigger {
            background: transparent !important;
            border-left: 1px solid #ced4da !important;
          }

          .p-datatable .p-datatable-tbody > tr > td .p-dropdown:not(.p-disabled):hover {
            border-color: #007ad9 !important;
          }

          .p-datatable .p-datatable-tbody > tr > td .p-dropdown:not(.p-disabled).p-focus {
            outline: 0 none !important;
            outline-offset: 0 !important;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
            border-color: #007ad9 !important;
          }

          .p-inputtextarea {
            resize: vertical !important;
            min-height: 60px !important;
          }

          .p-datatable .p-datatable-tbody > tr > td.p-editable-column {
            cursor: pointer !important;
          }

          .p-datatable .p-datatable-tbody > tr > td.p-cell-editing .p-dropdown,
          .p-datatable .p-datatable-tbody > tr > td.p-cell-editing .p-calendar {
            border: 2px solid #007ad9 !important;
          }

          .p-datatable .p-datatable-tbody > tr > td {
            padding: 4px !important;
          }

          .p-datatable .p-datatable-tbody > tr > td.p-cell-editing {
            padding: 2px !important;
          }

          .p-datatable .p-datatable-tbody > tr > td.p-editable-column:hover {
            background-color: #f8f9fa !important;
          }

          .p-datatable .p-datatable-tbody > tr > td.p-disabled {
            cursor: default !important;
            background-color: #f5f5f5 !important;
            color: #6c757d !important;
          }

          .p-datatable .p-datatable-tbody > tr > td > div {
            min-height: 30px;
            display: flex;
            align-items: center;
            width: 100%;
          }
        `}
      </style>

      <Accordion>
        <AccordionTab
          className='acc-pad'
          header={
            <div className="col-12 flex justify-content-between parent-full-width">
              <div className='col-9'>{data.label}</div>
              <div className='col-3 flex' >
                <span
                  className="badge d-inline-block text-capitalize"
                  style={{
                    backgroundColor: ['#f4e9c4', '#e99e34'],
                    color,
                    marginLeft: 'auto',
                    fontSize: '0.75rem',
                    padding: '6px 12px',
                    borderRadius: '5px',
                    minWidth: '100px',
                    textAlign: 'center'
                  }}
                >
                  {data.name}
                </span>
                <span
                  className="badge d-inline-block text-capitalize"
                  style={{
                    backgroundColor: bg,
                    color,
                    marginLeft: 'auto',
                    fontSize: '0.75rem',
                    padding: '6px 12px',
                    borderRadius: '5px',
                    minWidth: '100px',
                    textAlign: 'center'
                  }}
                >
                  {displayType}
                </span>

              </div>
            </div>
          }
        >
          <div className="accordion-body">
            <div className='flex justify-content-end m-2'>
              {data.assignedFramework && (
                <div className="d-flex flex-wrap gap-2 mt-2">
                  {Object.entries(data.assignedFramework).map(([frameworkId, labels]) =>
                    labels.map((labelVal, i) => (
                      <span
                        key={`${frameworkId}-${i}`}
                        className="badge bg-primary text-white px-2 py-1 rounded"
                        style={{ fontSize: "0.85rem" }}
                      >
                        {frameworkMap[frameworkId]} - {labelVal}
                      </span>
                    ))
                  )}
                </div>
              )}
            </div>
            {/* Response Table */}
            {inputTypes.includes(data.type) & data.type !== 'tableadd' ? <div className="table-responsive mb-3">
              <table className="table table-bordered">
                <thead className="table-light">
                  <tr>
                    <th>From</th>
                    <th>Entity</th>
                    <th>Response</th>
                  </tr>
                </thead>
                <tbody>
                  {responsesToShow.map((row, idx) =>
                    row.user !== 'Consolidate' ? (
                      <tr key={idx}>
                        <td>{getUser(Number(row.user))}</td>
                        <td>{row.entity}</td>
                        <td>{renderAnswer(row.answer)}</td>
                      </tr>
                    ) : null
                  )}
                </tbody>
              </table>
            </div> :

              <div className="mb-4">
                {(() => {
                  let tableHeaders = [];

                  // Extract headers for tableadd
                  if (responsesToShow && responsesToShow.length > 0) {
                    const firstResponse = responsesToShow.find(r => r.answer && Array.isArray(r.answer) && r.answer.length > 0);
                    if (firstResponse && firstResponse.answer[0]) {
                      tableHeaders = Object.keys(firstResponse.answer[0]);
                    }
                  }

                  return responsesToShow.filter(row => row.user !== 'Consolidate').map((userResponse, userIdx) => (
                    <div key={userIdx} className="w-100 mb-4">
                      <div className="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                        <h6 className="fw-bold mb-0">
                          User: {getUser(Number(userResponse.user))} | Entity: {userResponse.entity || 'N/A'}
                        </h6>
                       
                      </div>

                      {userResponse.answer && Array.isArray(userResponse.answer) && userResponse.answer.length > 0 ? (
                        <div className="border rounded p-2 bg-light">
                          <DataTable
                            value={userResponse.answer}
                            showGridlines
                            className="p-datatable-sm"
                            style={{ width: '100%' }}
                            scrollable
                            scrollHeight="200px"
                          >
                          {tableHeaders.map((header, headerIndex) => (
                            <Column
                              key={`user-${userIdx}-${header}-${headerIndex}`}
                              field={header}
                              header={header}
                              body={(rowData) => {
                                const cellData = rowData[header];
                                if (!cellData || !cellData.data) return '-';

                                const value = cellData.data.value;

                                switch (cellData.type) {
                                  case 1:
                                  case 2:
                                  case 3:
                                    return value || '-';
                                  case 4:
                                    if (cellData.data.values && value !== null && value !== undefined) {
                                      const selectedOption = cellData.data.values.find(opt => opt.value === value);
                                      return selectedOption ? selectedOption.label : value;
                                    }
                                    return value || '-';
                                  case 5:
                                    return cellData.data.label || '-';
                                  case 6:
                                    return value ? moment(value).format('DD-MM-YYYY') : '-';
                                  default:
                                    return value || '-';
                                }
                              }}
                            />
                          ))}
                          </DataTable>
                        </div>
                      ) : (
                        <div className="text-muted p-3 border rounded">
                          No data submitted by this user
                        </div>
                      )}
                    </div>
                  ));
                })()}
              </div>

            }
           {  data.type !== 'tableadd' &&  <div className="table-responsive mb-3">
              <table className="table table-bordered">
                <thead className="table-light">
                  <tr>
                    <th>Consolidated Response</th>
                  </tr>
                </thead>
                <tbody>
                  {responsesToShow.map((row, idx) =>
                    row.user === 'Consolidate' ? (
                      <tr key={idx}>

                        <td>{renderAnswer(row.answer)}</td>
                      </tr>
                    ) : null
                  )}
                </tbody>
              </table>
            </div>}

            {/* Input Label */}
           

            {/* Input Field */}
            <div className="mb-3">
              {data.type === 'text' && (
                <InputText className='col-5'
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                />
              )}
              {data.type === 'textarea' && (
                <InputTextarea className='col-5'
                  rows={3}
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                />
              )}
              {data.type === 'number' && (
                <InputNumber className='col-5'
                  min={0}

                  value={inputValue}
                  onChange={(e) => setInputValue(e.value)}
                />
              )}
              {data.type === 'date' && (
                <Calendar className='col-5'

                  value={inputValue}
                  onChange={(e) => setInputValue(e.value)}
                />
              )}
              {data.type === 'file' && (
                <input
                  className="form-control"
                  type="file"
                  onChange={(e) => { const form = new FormData(); form.append('file', e.target.files[0]); APIServices.post(API.FilesUpload, form).then((res) => { setInputValue(res?.data?.files?.[0]?.originalname) }) }}
                />
              )}
              {data.type === 'select' && (
                <Dropdown className='col-5'
                  value={
                    inputValue
                  }
                  options={data?.values || []}
                  onChange={(e) => {
                    setInputValue(e?.value || null);
                  }}
                />
              )}
              {['radio-group', 'checkpoint'].includes(data.type) && (
                <div>
                  {data.values.map((cb, cbind) => {
                    return (
                      <div className="p-2 flex text-justify fs-14 fw-5 align-items-center" >
                        <RadioButton inputId={"rg" + cbind} name={cb.label} value={cb.value} onChange={(e) => setInputValue(e.value)} checked={inputValue === cb.value} />

                        <label htmlFor={"rg" + cbind} className="ml-2">{cb.label}</label>
                      </div>
                    )
                  })}

                </div>
              )}
              {data.type === 'checkbox-group' && (
                <div>
                  {data.values.map((cb, cbind) => {
                    console.log(inputValue)
                    return (
                      <div className="flex text-justify fs-14 fw-5" style={{ marginBottom: 10 }}>
                        <Checkbox inputId={"cb" + cbind} name={cb.label} value={cb.value} onChange={() => {
                          const val = cb.value;
                          setInputValue(prev => {
                            if (!Array.isArray(prev)) prev = [];
                            return prev.includes(val)
                              ? prev.filter(v => v !== val)
                              : [...prev, val];
                          });
                        }} checked={Array.isArray(inputValue) && inputValue.includes(cb.value)} />
                        <label htmlFor={"cb" + cbind} className="ml-2">{cb.label}</label>
                      </div>
                    )
                  })

                  }

                </div>
              )}



              {/* Consolidated Response Section for tableadd */}
              {data.type === 'tableadd' && (
                <div className="col-12 flex flex-column" style={{ marginBottom: 5, padding: 5 }}>
          

                  <div className="flex justify-content-end" style={{ margin: 10, width: '100%' }}>
                    {(Array.isArray(inputValue) ? inputValue.length : 0) < (data.maxrowlimit || 10) && (
                      <Button
                        onClick={() => { addRow(data.newrow, data, 0) }}
                        icon='pi pi-plus'
                      />
                    )}
                  </div>

                  <DataTable
                    scrollable
                    showGridlines
                    className="fullheight"
                    style={{ width: '100%', maxHeight: 300 }}
                    value={Array.isArray(inputValue) ? inputValue : []}
                    editMode="cell"
                    onCellEditInit={(e) => {
                      console.log('Cell edit init:', e);
                      try {
                        // Ensure the cell is properly initialized for editing
                        const { rowIndex, field: columnField } = e;
                        console.log('Initializing edit for row:', rowIndex, 'field:', columnField);
                      } catch (error) {
                        console.error('Error in onCellEditInit:', error);
                      }
                    }}
                    onCellEditCancel={(e) => {
                      console.log('Cell edit cancel:', e);
                      try {
                        // Handle cancel gracefully
                        const { rowIndex, field: columnField } = e;
                        console.log('Cancelled edit for row:', rowIndex, 'field:', columnField);
                      } catch (error) {
                        console.error('Error in onCellEditCancel:', error);
                      }
                    }}
                  >
                    {(() => {
                      console.log('=== DATATABLE HEADERS DEBUG ===');
                      console.log('data.value (headers):', data.value);
                      console.log('inputValue:', inputValue);
                      console.log('data object:', data);

                      if (!data.value || data.value.length === 0) {
                        console.warn('No headers found for tableadd!');
                        return <Column header="No Headers" body={() => 'No headers defined'} />;
                      }

                      return data.value.map((h, index) => {
                      return <Column
                        key={`${data.name}-${h}`}
                        bodyClassName={(rowData) => {
                          return rowData[h] && rowData[h].type === 5 ? 'p-disabled' : ''
                        }}
                        header={h}
                        body={(rowData) => { return renderTableData(rowData[h]); }}
                        editor={(options) => {
                          console.log('=== EDITOR SETUP DEBUG ===');
                          console.log('Header:', h);
                          console.log('Original options:', options);
                          console.log('options.rowData:', options.rowData);
                          console.log('options.rowData[h]:', options.rowData[h]);

                          // Get the cell data to determine the item type and options
                          const cellData = options.rowData[h];
                          if (!cellData) {
                            console.log('No cellData found for header:', h);
                            return null;
                          }

                          // Get the most up-to-date value from inputValue first, then fallback to cellData
                          let currentValue = '';
                          const currentFormData = Array.isArray(inputValue) ? inputValue : [];
                          const rowIndex = options.rowIndex;

                          if (currentFormData && currentFormData[rowIndex] && currentFormData[rowIndex][h] && currentFormData[rowIndex][h].data) {
                            currentValue = currentFormData[rowIndex][h].data.value !== undefined ? currentFormData[rowIndex][h].data.value : '';
                            console.log('Using updated inputValue value:', currentValue);
                          } else if (cellData?.data?.value !== undefined) {
                            currentValue = cellData.data.value !== undefined ? cellData.data.value : '';
                            console.log('Using cellData value:', currentValue);
                          }

                          console.log('Final current value for editor:', currentValue);
                          console.log('cellData.data:', cellData.data);
                          console.log('cellData.type:', cellData.type);

                          // Create modified options with the current value
                          const modifiedOptions = {
                            ...options,
                            value: currentValue,
                            field: `field_${index}`,
                            headerName: h,
                            cellIndex: index
                          };

                          console.log('Modified options:', modifiedOptions);
                          return renderEditor(modifiedOptions, cellData);
                        }}
                        onCellEditComplete={(e) => {
                          // Create a custom event with the header field
                          const modifiedEvent = {
                            ...e,
                            field: `field_${index}`,
                            headerName: h,
                            cellIndex: index,
                            columnField: h
                          };
                          onCellEditComplete(modifiedEvent, data);
                        }}
                      />;
                      });
                    })()}
                    <Column
                      header='Action'
                      style={{ width: 50 }}
                      body={(rowData, e) => { return actionTemplate(rowData, e.rowIndex, data, 0) }}
                    />
                  </DataTable>

                  {/* Validation message */}
                  {data.type === 'tableadd' && (
                    <div className="mt-2">
                      <small className="text-muted">
                        <i className="pi pi-info-circle me-1"></i>
                        Highlighted cells with red borders are mandatory fields
                      </small>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Comment Section */}
            <div className="mb-2 mt-3">
              <label className="form-label fw-bold">Comment / Remarks</label>
              <textarea
                className="form-control"
                rows={3}
                value={consolidateText}
                onChange={(e) => setConsolidateText(e.target.value)}
              />
            </div>

            {/* Buttons */}
            <div className="d-flex gap-2">
              <Button
                label='Save'
                onClick={() => {
                  console.log('Save button clicked');
                  handleSave(data, "Completed")
                }}
              />

              <Button
                text
                className='mandatory'
                onClick={handleCancel}
                label='Cancel'
              />

            </div>

          </div>
        </AccordionTab>
      </Accordion>
    </>
  )
};

export default SectionBox;
